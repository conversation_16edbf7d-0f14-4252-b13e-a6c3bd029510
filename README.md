# Adori AI - Video Creation Platform

Adori AI is a modern web application that converts text, blogs, ideas, and audio into engaging videos using AI technology.

## Table of Contents

- [Adori AI - Video Creation Platform](#adori-ai---video-creation-platform)
  - [Table of Contents](#table-of-contents)
  - [Overview](#overview)
  - [Tech Stack](#tech-stack)
  - [Project Structure](#project-structure)
  - [Setup Instructions](#setup-instructions)
    - [Prerequisites](#prerequisites)
    - [Local Development](#local-development)
    - [Environment Variables](#environment-variables)
  - [Development Guidelines](#development-guidelines)
    - [Route Groups and Components](#route-groups-and-components)
    - [Component Structure](#component-structure)
    - [File Naming Conventions](#file-naming-conventions)
  - [Coding Standards](#coding-standards)
    - [TypeScript](#typescript)
    - [React Best Practices](#react-best-practices)
    - [Styling](#styling)
    - [State Management](#state-management)
    - [Code Formatting](#code-formatting)
    - [Editor Configuration](#editor-configuration)
- [Local Supabase Development Setup Guide](#local-supabase-development-setup-guide)
  - [Prerequisites](#prerequisites-1)
  - [Step 1: Install Docker Desktop](#step-1-install-docker-desktop)
    - [For macOS or Windows:](#for-macos-or-windows)
    - [For Linux:](#for-linux)
  - [Step 2: Install Supabase CLI](#step-2-install-supabase-cli)
  - [Step 3: Initialize a Supabase Project](#step-3-initialize-a-supabase-project)
  - [Step 4: Start Supabase Locally](#step-4-start-supabase-locally)
  - [Step 5: Access Your Local Supabase](#step-5-access-your-local-supabase)
  - [Step 6: Managing Your Local Database](#step-6-managing-your-local-database)
    - [Export Production Schema/Data (Optional)](#export-production-schemadata-optional)
    - [Import Schema/Data to Local Instance](#import-schemadata-to-local-instance)
    - [Verify Your Tables](#verify-your-tables)
  - [Step 7: Link Local Project to Supabase Cloud (Optional)](#step-7-link-local-project-to-supabase-cloud-optional)
  - [Step 8: Stop Supabase Locally](#step-8-stop-supabase-locally)
  - [Step 9: Daily Development Workflow](#step-9-daily-development-workflow)
    - [Starting Work (After System Reboot)](#starting-work-after-system-reboot)
    - [Ending Work Session](#ending-work-session)
  - [Troubleshooting Tips](#troubleshooting-tips)
    - [Common Issues and Solutions](#common-issues-and-solutions)
    - [Useful Commands](#useful-commands)
  - [Important Notes](#important-notes)
  - [Next Steps](#next-steps)
  - [License](#license)

## Overview

Adori AI is a platform that allows users to create videos from various content types:

- Ideas to Video
- Blog to Video
- Text to Video
- PDF to Video
- Audio to Video
- Podcast to Video

The platform features a modern UI with dark/light mode support, authentication, and a dashboard for managing video creation projects.

## Tech Stack

- **Framework**: [Next.js 15](https://nextjs.org/) with App Router
- **Language**: [TypeScript](https://www.typescriptlang.org/)
- **Styling**: [Tailwind CSS](https://tailwindcss.com/)
- **UI Components**: [shadcn/ui](https://ui.shadcn.com/)
- **Authentication**: [Better Auth](https://www.better-auth.com/)
- **Video Creation**: [Remotion](https://www.remotion.dev/)
- **Package Manager**: [Bun](https://bun.sh/) for local development, npm for Docker/cloud

## Project Structure

The project follows a structured approach with route groups and component organization:

```
src/
├── app/
│   ├── (auth)/              # Authentication routes
│   │   ├── sign-in/
│   │   └── sign-up/
│   ├── (dashboard)/         # Dashboard routes
│   │   ├── _components/     # Shared dashboard components
│   │   ├── create-video/    # Video creation routes
│   │   │   ├── _components/ # Video creation specific components
│   │   │   ├── idea/        # Idea to video route
│   │   │   ├── blog/        # Blog to video route
│   │   │   └── ...          # Other conversion routes
│   │   ├── drafts/          # Drafts management
│   │   ├── my-videos/       # User's videos
│   │   ├── pricing/         # Pricing plans
│   │   │   ├── _components/ # Pricing specific components
│   │   │   └── examples/    # Example pricing scenarios
│   │   └── settings/        # User settings
│   ├── (home)/              # Public home page
│   ├── globals.css          # Global styles
│   └── layout.tsx           # Root layout
├── components/              # Global shared components
│   ├── ui/                  # UI components (shadcn)
│   └── ...                  # Other global components
├── lib/                     # Utility functions and helpers
└── ...                      # Other directories
```

## Setup Instructions

### Prerequisites

- Node.js 18+
- Bun (recommended for local development)
- npm (for Docker/cloud environments)

### Local Development

1. Clone the repository:

   ```bash
   git clone https://github.com/your-username/adori-ai-v2.git
   cd adori-ai-v2
   ```

2. Install dependencies:

   ```bash
   bun install
   ```

3. Set up environment variables:

   Copy the existing `.env.example` file to create your environment file:

   ```bash
   cp .env.example .env
   ```

   Edit `.env` with your actual configuration values.

4. Run the development server:

   ```bash
   bun run dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

6. Set up Stripe webhook testing with ngrok:

   Install ngrok to create a secure tunnel to your local development server:

   ```bash
   brew install ngrok
   ```

   Start the ngrok tunnel to expose your local server:

   ```bash
   ngrok http 3000
   ```

   Copy the generated HTTPS forwarding URL (e.g. https://abc123.ngrok.io) and use it in your Stripe Dashboard:
   1. Go to Stripe Dashboard > Developers > Webhooks
   2. Click "Add endpoint"
   3. Paste your ngrok URL and append `/api/stripe/webhook`
   4. Select the events you want to test
   5. Save the webhook endpoint

### Environment Variables

The project requires several environment variables to function properly. Refer to the `.env.example` file for the complete list of required variables and their descriptions.

To obtain the necessary API keys:

1. **Better Auth**: Configure authentication settings in your environment
2. **OpenAI API**: Sign up at [platform.openai.com](https://platform.openai.com)
3. **Remotion**: Get your API key from [remotion.dev](https://www.remotion.dev)

## Development Guidelines

### Route Groups and Components

- **Route Groups**: Use route groups with parentheses (e.g., `(dashboard)`, `(auth)`) for organizing related routes
- **Component Organization**:
  - Global components go in `/components`
  - Route-specific components go in `_components` folders within their respective route groups
  - UI components from shadcn go in `/components/ui`

### Component Structure

- **Component Creation**:
  - Create small, focused components (< 50 lines)
  - Follow atomic design principles
  - Use shadcn/ui components when possible
  - Each component should have a single responsibility

- **Component Organization**:
  - Create a separate file for each component
  - Group related components in a directory
  - Use an index.ts file to export components from a directory
  - Use proper TypeScript interfaces for props

### File Naming Conventions

- Use kebab-case for file and directory names (e.g., `conversion-card.tsx`)
- Use PascalCase for component names (e.g., `ConversionCard`)
- Use camelCase for variables, functions, and instances
- Suffix component files with `.tsx` and utility files with `.ts`

## Coding Standards

### TypeScript

- Use TypeScript for all new code
- Define interfaces for component props
- Use proper type exports with `export type`
- Avoid using `any` type
- Use React.FC for functional components with explicit prop types

### React Best Practices

- Use functional components with hooks
- Minimize the use of `useEffect` and prefer server components when possible
- Implement proper error handling with early returns
- Use proper form handling with TypeScript types
- Create reusable components for common UI patterns

### Styling

- Use Tailwind CSS for styling
- Follow a mobile-first approach
- Use the `cn` utility for conditional class names
- Use shadcn/ui components and customize as needed
- Maintain consistent spacing and layout patterns

### State Management

- Use React hooks for local state
- Use React Context for shared state when needed
- Implement proper form state management
- Use controlled components for form inputs

### Code Formatting

We use Prettier for consistent code formatting across the project:

- Single quotes for strings and JSX attributes
- No semicolons
- 2 spaces for indentation
- 80 character line length
- Arrow function parentheses only when needed
- Trailing commas in objects and arrays

Run formatting commands:

```bash
# Format all files
bun run format

# Check formatting without changing files
bun run format:check
```

### Editor Configuration

The project includes configuration files to ensure consistent development experience:

- `.prettierrc` - Prettier configuration
- `.editorconfig` - Editor-agnostic configuration
- `.vscode/settings.json` - VS Code specific settings

For VS Code users, install the following extensions:

- Prettier - Code formatter
- ESLint
- Tailwind CSS IntelliSense

# Local Supabase Development Setup Guide

This comprehensive guide covers everything you need to install Docker Desktop, set up Supabase locally, and manage your local development environment efficiently.

## Prerequisites

Before starting, ensure you have:

- A Unix-like OS (macOS, Linux) or Windows with Docker Desktop support
- Node.js and npm installed (for Supabase CLI)
- Supabase account and project (optional, for linking remote project)

---

## Step 1: Install Docker Desktop

Docker provides the container runtime necessary to run Supabase services locally.

### For macOS or Windows:

1. Download Docker Desktop from: https://www.docker.com/products/docker-desktop
2. Follow the installation instructions for your operating system
3. Launch Docker Desktop after installation
4. Confirm Docker daemon is running (look for the whale icon in system tray/menu bar)

### For Linux:

Follow the Docker Engine installation guide for your specific Linux distribution from the Docker documentation.

---

## Step 2: Install Supabase CLI

The Supabase CLI manages local Supabase instances and migrations.

```bash
npm install supabase --save-dev
```

Verify installation:

```bash
supabase --version
```

---

## Step 3: Initialize a Supabase Project

Navigate to your workspace directory and initialize a new Supabase project:

```bash
npx supabase init
```

This creates a `supabase` folder with configuration files for your local project.

---

## Step 4: Start Supabase Locally

Make sure Docker Desktop is running, then start Supabase:

```bash
npx supabase start
```

This command will:

- Spin up necessary Docker containers (Postgres DB, API, Studio, etc.)
- Provide URLs to access Supabase Studio and APIs
- Display connection details for your local instance

**Note the output URLs and credentials for accessing your local Supabase Studio and APIs.**

---

## Step 5: Access Your Local Supabase

After starting, you can access:

- **Supabase Studio**: Usually at `http://localhost:54323`
- **Database URL**: Direct connection to your local Postgres instance
- **API URL**: For making API calls to your local instance

---

## Step 6: Managing Your Local Database

### Export Production Schema/Data (Optional)

If you have an existing Supabase project and want to replicate it locally:

**Export Schema:**

```bash
npx supabase db dump --db-url "<YOUR_PROD_DB_URL>" --schema public --file supabase/_dumps/prod-schema.sql
```

**Export Data:**

```bash
npx supabase db dump --db-url "<YOUR_PROD_DB_URL>" --data-only --schema public --file supabase/_dumps/prod-data.sql
```

### Import Schema/Data to Local Instance

**Find your running Supabase Postgres container:**

```bash
docker ps --format '{{.Names}}' | grep supabase_db
```

**Load schema into local database:**

```bash
docker exec -i <container_name> psql -U postgres -d postgres -f - < supabase/_dumps/prod-schema.sql
```

**Load data into local database:**

```bash
docker exec -i <container_name> psql -U postgres -d postgres -f - < supabase/_dumps/prod-data.sql
```

### Verify Your Tables

Check that tables were created successfully:

```bash
docker exec -i <container_name> psql -U postgres -d postgres -c "\dt public.*"
```

---

## Step 7: Link Local Project to Supabase Cloud (Optional)

If you want to sync with a remote Supabase project:

1. Get your project reference ID from Supabase Dashboard → Project Settings → Project Details
2. Link your local project:

```bash
npx supabase link --project-ref <YOUR_PROJECT_REF>
```

---

## Step 8: Stop Supabase Locally

When you're done developing, stop the local instance:

```bash
npx supabase stop
```

Your local data is preserved in Docker volumes and will be available when you restart.

---

## Step 9: Daily Development Workflow

### Starting Work (After System Reboot)

1. **Start Docker Desktop** (ensure Docker daemon is running)
2. **Navigate to your project directory**
3. **Start Supabase locally:**
   ```bash
   npx supabase start
   ```
4. **(Optional) Import fresh data if needed**

### Ending Work Session

```bash
npx supabase stop
```

---

## Troubleshooting Tips

### Common Issues and Solutions

**Docker not running:**

- Ensure Docker Desktop is launched and the daemon is active
- Look for the whale icon in your system tray/menu bar

**Port conflicts:**

- Check if other services are using the same ports
- Stop conflicting services or modify Supabase port configuration

**Container issues:**

- View running containers: `docker ps`
- View all containers: `docker ps -a`
- Remove problematic containers: `docker rm <container_name>`

**Debugging commands:**

- Add `--debug` flag to Supabase commands for detailed logs
- Check Docker logs: `docker logs <container_name>`

### Useful Commands

**View Supabase status:**

```bash
npx supabase status
```

**Reset local database:**

```bash
npx supabase db reset
```

**Generate types for your schema:**

```bash
npx supabase gen types typescript --local > types/supabase.ts
```

---

## Important Notes

- Always ensure Docker Desktop is running before starting Supabase locally
- Supabase data is persisted in Docker volumes unless explicitly removed
- Avoid running Podman simultaneously as it may conflict with Docker sockets
- Use the non-pooling DB URL for CLI commands requiring direct database connection
- Local development uses different URLs and API keys than production

---

## Next Steps

Once your local Supabase is running:

1. **Explore Supabase Studio** at the provided local URL
2. **Create tables and configure Row Level Security (RLS)**
3. **Test your application** against the local instance
4. **Use migrations** to version control your database schema changes
5. **Deploy changes** to production when ready

---

This guide provides a complete workflow from Docker installation to managing local Supabase development efficiently. Happy coding!

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.
