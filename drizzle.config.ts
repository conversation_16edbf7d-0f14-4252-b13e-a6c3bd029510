import type { Config } from 'drizzle-kit'

export default {
  schema: './src/db/schema.ts',
  out: './drizzle',
  dialect: 'postgresql',
  dbCredentials: {
    host: process.env.POSTGRES_HOST!,
    port: process.env.NODE_ENV === 'development' ? 54322 : 5432,
    user: process.env.POSTGRES_USER!,
    password: process.env.POSTGRES_PASSWORD!,
    database: process.env.POSTGRES_DATABASE!,
    ssl:
      process.env.NODE_ENV === 'development'
        ? {
            rejectUnauthorized: false, // For development
          }
        : {
            rejectUnauthorized: true, // For production
          }, // false for local development
  },
} satisfies Config
