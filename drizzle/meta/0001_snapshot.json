{"version": "7", "dialect": "postgresql", "id": "0001_rename_blog_images_to_imported_media", "prevId": "0000_whole_spyke", "tables": {"media_assets": {"name": "media_assets", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "notNull": true}, "file_name": {"name": "file_name", "type": "text", "notNull": true}, "original_name": {"name": "original_name", "type": "text", "notNull": true}, "mime_type": {"name": "mime_type", "type": "text", "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "notNull": true}, "original_url": {"name": "original_url", "type": "text", "notNull": true}, "thumbnail_url": {"name": "thumbnail_url", "type": "text"}, "low_res_url": {"name": "low_res_url", "type": "text"}, "width": {"name": "width", "type": "integer"}, "height": {"name": "height", "type": "integer"}, "duration": {"name": "duration", "type": "numeric", "precision": 10, "scale": 3}, "quality": {"name": "quality", "type": "text"}, "fps": {"name": "fps", "type": "integer"}, "metadata": {"name": "metadata", "type": "jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "notNull": true, "default": "now()"}}, "indexes": {"idx_media_assets_user_id": {"name": "idx_media_assets_user_id", "columns": ["user_id"]}, "idx_media_assets_mime_type": {"name": "idx_media_assets_mime_type", "columns": ["mime_type"]}, "idx_media_assets_created_at": {"name": "idx_media_assets_created_at", "columns": ["created_at"]}}}, "projects": {"name": "projects", "schema": "", "columns": {"project_id": {"name": "project_id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "notNull": true}, "project_name": {"name": "project_name", "type": "text", "notNull": true}, "method": {"name": "method", "type": "text", "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "notNull": true, "default": "now()"}, "cover_color": {"name": "cover_color", "type": "text", "notNull": true}, "cover_pic": {"name": "cover_pic", "type": "text"}, "orientation": {"name": "orientation", "type": "text", "notNull": true}, "duration": {"name": "duration", "type": "text", "notNull": true}, "summary": {"name": "summary", "type": "text"}, "music": {"name": "music", "type": "jsonb"}, "speech": {"name": "speech", "type": "jsonb"}, "caption_settings": {"name": "caption_settings", "type": "jsonb"}, "background_video": {"name": "background_video", "type": "jsonb"}, "scenes": {"name": "scenes", "type": "jsonb"}, "event_id": {"name": "event_id", "type": "text"}, "run_id": {"name": "run_id", "type": "text"}, "imported_media": {"name": "imported_media", "type": "jsonb"}, "organization_id": {"name": "organization_id", "type": "text"}, "voice_regenerations": {"name": "voice_regenerations", "type": "integer", "notNull": true, "default": "0"}}, "indexes": {"idx_projects_user_id": {"name": "idx_projects_user_id", "columns": ["user_id"]}, "idx_projects_organization_id": {"name": "idx_projects_organization_id", "columns": ["organization_id"]}}}, "render_jobs": {"name": "render_jobs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "project_id": {"name": "project_id", "type": "text", "notNull": true}, "user_id": {"name": "user_id", "type": "text", "notNull": true}, "organization_id": {"name": "organization_id", "type": "text"}, "status": {"name": "status", "type": "text", "notNull": true}, "progress": {"name": "progress", "type": "integer", "default": "0"}, "public_url": {"name": "public_url", "type": "text"}, "thumbnail_url": {"name": "thumbnail_url", "type": "text"}, "error_message": {"name": "error_message", "type": "text"}, "render_method": {"name": "render_method", "type": "text"}, "export_name": {"name": "export_name", "type": "text"}, "export_resolution": {"name": "export_resolution", "type": "text"}, "created_at": {"name": "created_at", "type": "timestamp", "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "notNull": true, "default": "now()"}, "youtube_id": {"name": "youtube_id", "type": "text"}}, "indexes": {"idx_render_jobs_user_project": {"name": "idx_render_jobs_user_project", "columns": ["user_id", "project_id"]}, "idx_render_jobs_organization_id": {"name": "idx_render_jobs_organization_id", "columns": ["organization_id"]}, "idx_render_jobs_status": {"name": "idx_render_jobs_status", "columns": ["status"]}, "idx_render_jobs_created_at": {"name": "idx_render_jobs_created_at", "columns": ["created_at"]}}}, "stock_music": {"name": "stock_music", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "text", "notNull": true}, "genre": {"name": "genre", "type": "text", "notNull": true}, "mood": {"name": "mood", "type": "text", "notNull": true}, "artist_name": {"name": "artist_name", "type": "text", "notNull": true}, "artist_url": {"name": "artist_url", "type": "text"}, "provider": {"name": "provider", "type": "text", "notNull": true}, "license_id": {"name": "license_id", "type": "text", "notNull": true}, "source_url": {"name": "source_url", "type": "text"}, "preview_url": {"name": "preview_url", "type": "text", "notNull": true}, "duration_millis": {"name": "duration_millis", "type": "integer", "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "notNull": true, "default": "now()"}}}}}