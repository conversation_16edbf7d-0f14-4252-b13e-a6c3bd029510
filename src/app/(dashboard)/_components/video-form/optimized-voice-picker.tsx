'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Volume2, ChevronDown } from 'lucide-react'
import type { ElevenVoice } from '@/hooks/useElevenVoicesQuery'
import { VoicePickerModal } from '@/app/(scene-editor)/_components/voice-picker-modal'

interface OptimizedVoicePickerProps {
  voice: ElevenVoice | null
  onVoiceSelect: (voice: ElevenVoice) => void
  disabled?: boolean
}

export function OptimizedVoicePicker({
  voice,
  onVoiceSelect,
  disabled,
}: OptimizedVoicePickerProps) {
  const [isVoicePickerOpen, setIsVoicePickerOpen] = useState(false)

  const handleVoicePickerOpen = () => {
    // Open modal immediately - VoicePickerModal handles its own loading states
    // No artificial delays or full-page overlays needed
    setIsVoicePickerOpen(true)
  }

  const handleVoiceSelect = (selectedVoice: ElevenVoice) => {
    onVoiceSelect(selectedVoice)
    setIsVoicePickerOpen(false)
  }

  return (
    <>
      <div className='space-y-1'>
        <label className='text-sm font-medium'>
          Voice Selection <span className='text-red-500'>*</span>
        </label>
        <Button
          variant='outline'
          onClick={handleVoicePickerOpen}
          className='w-full h-9 justify-between text-xs'
          disabled={disabled}
        >
          <div className='flex items-center gap-2'>
            <Volume2 className='h-3 w-3' />
            {voice ? (
              <span className='truncate'>{voice.name}</span>
            ) : (
              <span className='text-muted-foreground'>Choose a voice</span>
            )}
          </div>
          <div className='flex items-center gap-1'>
            <ChevronDown className='h-3 w-3' />
          </div>
        </Button>
        {voice && (
          <p className='text-xs text-muted-foreground'>
            {voice.labels?.gender && `${voice.labels.gender} • `}
            {voice.labels?.accent && `${voice.labels.accent} accent • `}
            {voice.labels?.language && `${voice.labels.language}`}
          </p>
        )}
      </div>

      {/* Only render modal when opened - reduces initial bundle size */}
      {isVoicePickerOpen && (
        <VoicePickerModal
          isOpen={isVoicePickerOpen}
          onClose={() => setIsVoicePickerOpen(false)}
          onSelectVoice={handleVoiceSelect}
          showApplyToAllScenes={false}
          isRegenerationMode={false}
        />
      )}
    </>
  )
}
