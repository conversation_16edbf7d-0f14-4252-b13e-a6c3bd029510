'use client'

import { useState, useCallback, useMemo } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import { ChevronDown, ChevronRight, Captions, AlertCircle } from 'lucide-react'
import { CaptionSegmentEditor } from './caption-segment-editor'
import { CaptionEditorProps, CaptionSegment } from './types'
import { updateSegmentTiming } from './caption-utils'
import { useVideoStore } from '@/store/video-store'

export function CaptionEditor({
  scene,
  onUpdateScene,
  onUpdateProjectSpeechCaptions,
  isCollapsed = true,
}: CaptionEditorProps) {
  const [isOpen, setIsOpen] = useState(!isCollapsed)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const { project } = useVideoStore()

  // Convert captions to our internal format, handling both text-to-video and audio/podcast formats
  const captions = useMemo((): CaptionSegment[] => {
    // First, try scene.captions (text-to-video format)
    if (scene.captions && scene.captions.length > 0) {
      return scene.captions.map(caption => ({
        start: caption.start,
        end: caption.end,
        text: caption.text,
        words: caption.words || [],
      }))
    }

    // Then, try speech.transcript.captions (audio/podcast format)
    if (
      project?.speech?.transcript?.captions &&
      project.speech.transcript.captions.length > 0
    ) {
      return project.speech.transcript.captions.map(caption => ({
        start: caption.start,
        end: caption.end,
        text: caption.sentence, // Note: uses 'sentence' instead of 'text'
        words: (caption.wordBoundries || []).map(word => ({
          start: word.start,
          end: word.end,
          word: word.word,
        })),
      }))
    }

    return []
  }, [scene.captions, project?.speech?.transcript?.captions])

  // Check if scene has captions
  const hasCaptions = captions.length > 0
  const totalWords = captions.reduce(
    (sum, caption) => sum + caption.words.length,
    0
  )

  // Determine if we're working with speech captions or scene captions
  const isUsingSpeechCaptions = useMemo(() => {
    return (
      !scene.captions?.length && project?.speech?.transcript?.captions?.length
    )
  }, [scene.captions, project?.speech?.transcript?.captions])

  // Handle caption updates
  const handleCaptionUpdate = useCallback(
    (segmentIndex: number, updatedSegment: CaptionSegment) => {
      const newCaptions = [...captions]
      newCaptions[segmentIndex] = updateSegmentTiming(updatedSegment)

      if (isUsingSpeechCaptions && onUpdateProjectSpeechCaptions) {
        // For audio/podcast projects, update the project-level speech transcript captions
        const speechFormatCaptions = newCaptions.map(caption => ({
          start: caption.start,
          end: caption.end,
          sentence: caption.text, // Note: speech format uses 'sentence' instead of 'text'
          wordBoundries: caption.words.map(word => ({
            start: word.start,
            end: word.end,
            word: word.word,
          })),
        }))

        onUpdateProjectSpeechCaptions(speechFormatCaptions)
      } else {
        // Update scene.captions format (text-to-video)
        const sceneFormatCaptions = newCaptions.map(caption => ({
          start: caption.start,
          end: caption.end,
          text: caption.text,
          words: caption.words,
        }))

        onUpdateScene(scene.id, { captions: sceneFormatCaptions })
      }

      setHasUnsavedChanges(false)
    },
    [
      captions,
      scene.id,
      onUpdateScene,
      onUpdateProjectSpeechCaptions,
      isUsingSpeechCaptions,
    ]
  )

  if (!hasCaptions) {
    const isAudioProject =
      project?.method?.toLowerCase().includes('audio') ||
      project?.method?.toLowerCase().includes('podcast')

    return (
      <div className='mt-3 p-3 border rounded-lg bg-muted/30'>
        <div className='flex items-center gap-2 text-sm text-muted-foreground'>
          <Captions className='w-4 h-4' />
          <span>
            {isAudioProject
              ? 'No captions available. Upload audio with transcription to create captions.'
              : 'No captions available. Generate voiceover to create captions.'}
          </span>
        </div>
      </div>
    )
  }

  // Render the caption editing content
  const renderCaptionContent = () => (
    <div className='border rounded-lg bg-card'>
      {/* Header with actions */}
      <div className='flex items-center justify-between p-3 border-b'>
        <div className='text-sm font-medium'>Edit Captions</div>
        <div className='flex items-center gap-2'></div>
      </div>

      {/* Caption segments */}
      <div className='p-3 space-y-3 max-h-96 overflow-y-auto'>
        {captions.map((caption, index) => (
          <CaptionSegmentEditor
            key={`${scene.id}-caption-${index}`}
            segment={caption}
            segmentIndex={index}
            onUpdate={updatedSegment =>
              handleCaptionUpdate(index, updatedSegment)
            }
            onMarkDirty={() => setHasUnsavedChanges(true)}
          />
        ))}
      </div>

      {/* Footer info */}
      <div className='px-3 py-2 border-t bg-muted/30 text-xs text-muted-foreground'>
        Click words to edit • Use Tab to navigate • Changes save automatically
      </div>
    </div>
  )

  // If isCollapsed is false, render content directly (for use in scene sections)
  if (!isCollapsed) {
    return <div className='mt-3'>{renderCaptionContent()}</div>
  }

  // Otherwise, render with collapsible wrapper (for standalone use)
  return (
    <div className='mt-3'>
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <Button
            variant='ghost'
            size='sm'
            className='w-full justify-between p-2 h-auto border rounded-lg hover:bg-muted/50'
          >
            <div className='flex items-center gap-2'>
              {isOpen ? (
                <ChevronDown className='w-4 h-4' />
              ) : (
                <ChevronRight className='w-4 h-4' />
              )}
              <Captions className='w-4 h-4' />
              <span className='text-sm font-medium'>Caption Editor</span>
              {hasUnsavedChanges && (
                <AlertCircle className='w-3 h-3 text-orange-500' />
              )}
            </div>
            <div className='flex items-center gap-2'>
              <Badge variant='outline' className='text-xs'>
                {captions.length} segments
              </Badge>
              <Badge variant='outline' className='text-xs'>
                {totalWords} words
              </Badge>
            </div>
          </Button>
        </CollapsibleTrigger>

        <CollapsibleContent className='mt-2'>
          {renderCaptionContent()}
        </CollapsibleContent>
      </Collapsible>
    </div>
  )
}
