'use client'

import React, { useState, useCallback } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Clock, Plus } from 'lucide-react'
import { WordEditor } from './word-editor'
import { CaptionSegment, CaptionWord } from './types'
import {
  recalculateWordTimingsAfterDeletion,
  calculateInsertedWordTiming,
  formatTime,
} from './caption-utils'

interface CaptionSegmentEditorProps {
  segment: CaptionSegment
  segmentIndex: number
  onUpdate: (updatedSegment: CaptionSegment) => void
  onMarkDirty: () => void
}

export function CaptionSegmentEditor({
  segment,
  segmentIndex,
  onUpdate,
  onMarkDirty,
}: CaptionSegmentEditorProps) {
  const [localWords, setLocalWords] = useState<CaptionWord[]>(segment.words)
  // Keep local state in sync with upstream segment changes (undo/redo)
  React.useEffect(() => {
    setLocalWords(segment.words)
  }, [segment.words, segmentIndex, segment.start, segment.end])

  // Handle word updates
  const handleWordUpdate = useCallback(
    (wordIndex: number, updatedWord: CaptionWord) => {
      const newWords = [...localWords]
      newWords[wordIndex] = updatedWord
      setLocalWords(newWords)

      const updatedSegment: CaptionSegment = {
        ...segment,
        words: newWords,
        text: newWords.map(w => w.word).join(' '),
      }

      onUpdate(updatedSegment)
      onMarkDirty()
    },
    [localWords, segment, onUpdate, onMarkDirty]
  )

  // Handle word deletion
  const handleWordDelete = useCallback(
    (wordIndex: number) => {
      if (localWords.length <= 1) {
        // Don't allow deleting the last word
        return
      }

      const newWords = recalculateWordTimingsAfterDeletion(
        localWords,
        wordIndex
      )
      setLocalWords(newWords)

      const updatedSegment: CaptionSegment = {
        ...segment,
        words: newWords,
        text: newWords.map(w => w.word).join(' '),
      }

      onUpdate(updatedSegment)
      onMarkDirty()
    },
    [localWords, segment, onUpdate, onMarkDirty]
  )

  // Handle word insertion
  const handleWordInsert = useCallback(
    (afterIndex: number) => {
      const newWordTiming = calculateInsertedWordTiming(
        localWords,
        afterIndex,
        'new'
      )

      const newWords = [...localWords]
      newWords.splice(afterIndex + 1, 0, newWordTiming)

      // If we split an existing word's duration, update the next word
      if (afterIndex < localWords.length - 1) {
        const nextWordIndex = afterIndex + 2 // After insertion
        if (nextWordIndex < newWords.length) {
          newWords[nextWordIndex].start = newWordTiming.end
        }
      }

      setLocalWords(newWords)

      const updatedSegment: CaptionSegment = {
        ...segment,
        words: newWords,
        text: newWords.map(w => w.word).join(' '),
      }

      onUpdate(updatedSegment)
      onMarkDirty()
    },
    [localWords, segment, onUpdate, onMarkDirty]
  )

  const segmentDuration = segment.end - segment.start

  return (
    <div className='border rounded-lg p-3 bg-background'>
      {/* Segment header */}
      <div className='flex items-center justify-between mb-3'>
        <div className='flex items-center gap-2'>
          <Badge variant='outline' className='text-xs'>
            Segment {segmentIndex + 1}
          </Badge>
          <div className='flex items-center gap-1 text-xs text-muted-foreground'>
            <Clock className='w-3 h-3' />
            <span>
              {formatTime(segment.start)} - {formatTime(segment.end)}
            </span>
            <span>({formatTime(segmentDuration)})</span>
          </div>
        </div>
      </div>

      {/* Words grid */}
      <div className='space-y-2'>
        <div className='flex flex-wrap gap-1'>
          {localWords.map((word, wordIndex) => (
            <React.Fragment key={`word-${wordIndex}`}>
              <WordEditor
                word={word}
                onUpdate={updatedWord =>
                  handleWordUpdate(wordIndex, updatedWord)
                }
                onDelete={() => handleWordDelete(wordIndex)}
                canDelete={localWords.length > 1}
              />

              {/* Insert button after each word (except last) */}
              {wordIndex < localWords.length - 1 && (
                <Button
                  variant='ghost'
                  size='sm'
                  className='h-6 w-6 p-0 opacity-50 hover:opacity-100'
                  onClick={() => handleWordInsert(wordIndex)}
                  title='Insert word after'
                >
                  <Plus className='w-3 h-3' />
                </Button>
              )}
            </React.Fragment>
          ))}

          {/* Add word at end */}
          <Button
            variant='ghost'
            size='sm'
            className='h-6 px-2 opacity-50 hover:opacity-100 text-xs'
            onClick={() => handleWordInsert(localWords.length - 1)}
            title='Add word at end'
          >
            <Plus className='w-3 h-3 mr-1' />
            Add
          </Button>
        </div>

        {/* Segment text preview */}
        <div className='text-xs text-muted-foreground bg-muted/30 p-2 rounded'>
          <strong>Text:</strong> {localWords.map(w => w.word).join(' ')}
        </div>
      </div>
    </div>
  )
}
