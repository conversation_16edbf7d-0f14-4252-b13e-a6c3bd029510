/**
 * Caption Editor Utilities
 * 
 * Utility functions for caption timing calculations and validation
 */

import { CaptionWord, CaptionSegment, ValidationError } from './types'

/**
 * Recalculate word timings after a word is deleted
 */
export function recalculateWordTimingsAfterDeletion(
  words: CaptionWord[],
  deletedIndex: number
): CaptionWord[] {
  if (words.length <= 1) return []
  
  const newWords = [...words]
  const deletedWord = newWords[deletedIndex]
  const deletedDuration = deletedWord.end - deletedWord.start
  
  newWords.splice(deletedIndex, 1)
  
  if (deletedIndex === 0) {
    // First word deleted - give duration to next word
    if (newWords.length > 0) {
      newWords[0].start = deletedWord.start
    }
  } else if (deletedIndex === words.length - 1) {
    // Last word deleted - give duration to previous word
    const prevIndex = deletedIndex - 1
    if (prevIndex >= 0 && prevIndex < newWords.length) {
      newWords[prevIndex].end = deletedWord.end
    }
  } else {
    // Middle word deleted - split duration between adjacent words
    const prevIndex = deletedIndex - 1
    const nextIndex = deletedIndex // After deletion, next word shifts down
    
    if (prevIndex >= 0 && nextIndex < newWords.length) {
      const halfDuration = deletedDuration / 2
      newWords[prevIndex].end += halfDuration
      newWords[nextIndex].start -= halfDuration
    }
  }
  
  return newWords
}

/**
 * Calculate timing for a new word inserted between existing words
 */
export function calculateInsertedWordTiming(
  words: CaptionWord[],
  insertAfterIndex: number,
  newWord: string
): { start: number; end: number; word: string } {
  if (words.length === 0) {
    return { start: 0, end: 1, word: newWord }
  }
  
  if (insertAfterIndex >= words.length - 1) {
    // Insert at end
    const lastWord = words[words.length - 1]
    const duration = 0.5 // Default duration for new words
    return {
      start: lastWord.end,
      end: lastWord.end + duration,
      word: newWord
    }
  }
  
  const currentWord = words[insertAfterIndex]
  const nextWord = words[insertAfterIndex + 1]
  const gap = nextWord.start - currentWord.end
  
  if (gap > 0.2) {
    // Use available gap
    const newStart = currentWord.end + 0.05
    const newEnd = nextWord.start - 0.05
    return {
      start: newStart,
      end: newEnd,
      word: newWord
    }
  } else {
    // Split next word's duration
    const splitDuration = (nextWord.end - nextWord.start) / 2
    const newStart = currentWord.end
    const newEnd = newStart + splitDuration
    
    // Update next word timing (will be handled by caller)
    return {
      start: newStart,
      end: newEnd,
      word: newWord
    }
  }
}

/**
 * Validate word timing constraints
 */
export function validateWordTiming(
  words: CaptionWord[],
  wordIndex: number,
  newStart: number,
  newEnd: number
): ValidationError[] {
  const errors: ValidationError[] = []
  
  // Check basic constraints
  if (newStart >= newEnd) {
    errors.push({
      type: 'negative_duration',
      message: 'Word end time must be after start time',
      wordIndex
    })
  }
  
  if (newStart < 0 || newEnd < 0) {
    errors.push({
      type: 'invalid_timing',
      message: 'Timing cannot be negative',
      wordIndex
    })
  }
  
  // Check overlaps with adjacent words
  if (wordIndex > 0) {
    const prevWord = words[wordIndex - 1]
    if (newStart < prevWord.end) {
      errors.push({
        type: 'overlap',
        message: 'Word overlaps with previous word',
        wordIndex
      })
    }
  }
  
  if (wordIndex < words.length - 1) {
    const nextWord = words[wordIndex + 1]
    if (newEnd > nextWord.start) {
      errors.push({
        type: 'overlap',
        message: 'Word overlaps with next word',
        wordIndex
      })
    }
  }
  
  return errors
}

/**
 * Update caption segment timing based on word changes
 */
export function updateSegmentTiming(segment: CaptionSegment): CaptionSegment {
  if (segment.words.length === 0) {
    return { ...segment, start: 0, end: 0, text: '' }
  }
  
  const firstWord = segment.words[0]
  const lastWord = segment.words[segment.words.length - 1]
  const text = segment.words.map(w => w.word).join(' ')
  
  return {
    ...segment,
    start: firstWord.start,
    end: lastWord.end,
    text
  }
}

/**
 * Format time for display (e.g., "1.23s")
 */
export function formatTime(seconds: number): string {
  return `${seconds.toFixed(2)}s`
}

/**
 * Parse time string back to number
 */
export function parseTime(timeString: string): number {
  const cleaned = timeString.replace(/[^0-9.]/g, '')
  const parsed = parseFloat(cleaned)
  return isNaN(parsed) ? 0 : parsed
}
