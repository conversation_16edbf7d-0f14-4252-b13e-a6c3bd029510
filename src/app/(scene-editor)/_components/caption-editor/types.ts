/**
 * Caption Editor Types
 *
 * Type definitions for caption editing functionality
 */

import { Scene, Caption } from '@/types/video'

export interface CaptionWord {
  start: number
  end: number
  word: string
}

export interface CaptionSegment {
  start: number
  end: number
  text: string
  words: CaptionWord[]
}

export interface CaptionEditorProps {
  scene: Scene
  onUpdateScene: (id: string, data: Partial<Scene>) => void
  onUpdateProjectSpeechCaptions?: (captions: Caption[]) => void
  isCollapsed?: boolean
}

export interface WordEditState {
  isEditing: boolean
  originalWord: string
  editedWord: string
  wordIndex: number
  segmentIndex: number
}

export interface TimingEditState {
  isEditing: boolean
  wordIndex: number
  segmentIndex: number
  originalStart: number
  originalEnd: number
  editedStart: string
  editedEnd: string
}

export interface CaptionEditHistory {
  captions: CaptionSegment[]
  timestamp: number
  description: string
}

export interface ValidationError {
  type: 'overlap' | 'negative_duration' | 'invalid_timing' | 'empty_word'
  message: string
  wordIndex?: number
  segmentIndex?: number
}
