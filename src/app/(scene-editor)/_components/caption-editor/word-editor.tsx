'use client'

import React, { useState, useRef, useEffect, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Trash2, Clock, Check, X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { CaptionWord } from './types'
import { formatTime, parseTime } from './caption-utils'
// Removed Tooltip imports - using simple hover overlay instead

interface WordEditorProps {
  word: CaptionWord
  onUpdate: (updatedWord: CaptionWord) => void
  onDelete: () => void
  canDelete: boolean
}

export function WordEditor({
  word,
  onUpdate,
  onDelete,
  canDelete,
}: WordEditorProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editedWord, setEditedWord] = useState(word.word)
  const [showTiming, setShowTiming] = useState(false)
  const [editedStart, setEditedStart] = useState(formatTime(word.start))
  const [editedEnd, setEditedEnd] = useState(formatTime(word.end))
  const [hasError, setHasError] = useState(false)

  const inputRef = useRef<HTMLInputElement>(null)

  // Focus input when editing starts
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus()
      inputRef.current.select()
    }
  }, [isEditing])

  // Handle word text editing
  const handleWordEdit = useCallback(() => {
    setIsEditing(true)
    setEditedWord(word.word)
  }, [word.word])

  const handleWordSave = useCallback(() => {
    if (editedWord.trim() && editedWord !== word.word) {
      onUpdate({
        ...word,
        word: editedWord.trim(),
      })
    }
    setIsEditing(false)
    setEditedWord(word.word)
  }, [editedWord, word, onUpdate])

  const handleWordCancel = useCallback(() => {
    setIsEditing(false)
    setEditedWord(word.word)
  }, [word.word])

  // Handle timing editing
  const handleTimingSave = useCallback(() => {
    const newStart = parseTime(editedStart)
    const newEnd = parseTime(editedEnd)

    // Basic validation
    if (newStart >= newEnd) {
      setHasError(true)
      return
    }

    onUpdate({
      ...word,
      start: newStart,
      end: newEnd,
    })

    setShowTiming(false)
    setHasError(false)
  }, [editedStart, editedEnd, word, onUpdate])

  const handleTimingCancel = useCallback(() => {
    setEditedStart(formatTime(word.start))
    setEditedEnd(formatTime(word.end))
    setShowTiming(false)
    setHasError(false)
  }, [word.start, word.end])

  // Handle keyboard events
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter') {
        e.preventDefault()
        handleWordSave()
      } else if (e.key === 'Escape') {
        e.preventDefault()
        handleWordCancel()
      }
    },
    [handleWordSave, handleWordCancel]
  )

  return (
    <div className='inline-flex items-center'>
      {isEditing ? (
        <div className='flex items-center gap-1'>
          <Input
            ref={inputRef}
            value={editedWord}
            onChange={e => setEditedWord(e.target.value)}
            onKeyDown={handleKeyDown}
            className='h-6 px-2 text-xs w-20 min-w-0'
            placeholder='word'
          />
          <Button
            variant='ghost'
            size='sm'
            className='h-6 w-6 p-0'
            onClick={handleWordSave}
          >
            <Check className='w-3 h-3 text-green-600' />
          </Button>
          <Button
            variant='ghost'
            size='sm'
            className='h-6 w-6 p-0'
            onClick={handleWordCancel}
          >
            <X className='w-3 h-3 text-red-600' />
          </Button>
        </div>
      ) : (
        <Popover open={showTiming} onOpenChange={setShowTiming}>
          <PopoverTrigger asChild>
            <div className='group relative inline-block'>
              <Badge
                variant='outline'
                className={cn(
                  'cursor-pointer hover:bg-muted text-xs px-2 py-1',
                  'transition-colors duration-200'
                )}
                onClick={handleWordEdit}
                onContextMenu={e => {
                  e.preventDefault()
                  setShowTiming(true)
                }}
              >
                {word.word}
              </Badge>

              {/* Hover actions - positioned below the word with proper isolation */}
              <div className='absolute -top-7 left-1/2 transform -translate-x-1/2 hidden group-hover:flex transition-opacity duration-200 items-center gap-1 bg-popover border rounded px-2 py-1 shadow-md z-50 pointer-events-auto'>
                <Button
                  variant='ghost'
                  size='sm'
                  className='h-5 w-5 p-0 hover:bg-muted'
                  onClick={e => {
                    e.preventDefault()
                    e.stopPropagation()
                    setShowTiming(true)
                  }}
                  title='Edit timing'
                >
                  <Clock className='w-3 h-3' />
                </Button>
                {canDelete && (
                  <Button
                    variant='ghost'
                    size='sm'
                    className='h-5 w-5 p-0 text-red-600 hover:text-red-700 hover:bg-red-50'
                    onClick={e => {
                      e.preventDefault()
                      e.stopPropagation()
                      onDelete()
                    }}
                    title='Delete word'
                  >
                    <Trash2 className='w-3 h-3' />
                  </Button>
                )}
              </div>
            </div>
          </PopoverTrigger>

          <PopoverContent className='w-64 p-3' align='start'>
            <div className='space-y-3'>
              <div className='text-sm font-medium'>Edit Timing</div>

              <div className='space-y-2'>
                <div>
                  <label className='text-xs text-muted-foreground'>
                    Start Time
                  </label>
                  <Input
                    value={editedStart}
                    onChange={e => setEditedStart(e.target.value)}
                    className='h-7 text-xs'
                    placeholder='0.00s'
                  />
                </div>

                <div>
                  <label className='text-xs text-muted-foreground'>
                    End Time
                  </label>
                  <Input
                    value={editedEnd}
                    onChange={e => setEditedEnd(e.target.value)}
                    className='h-7 text-xs'
                    placeholder='1.00s'
                  />
                </div>

                <div className='text-xs text-muted-foreground'>
                  Duration:{' '}
                  {formatTime(parseTime(editedEnd) - parseTime(editedStart))}
                </div>

                {hasError && (
                  <div className='text-xs text-red-600'>
                    Invalid timing: End must be after start
                  </div>
                )}
              </div>

              <div className='flex justify-end gap-2'>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={handleTimingCancel}
                  className='h-7 px-2 text-xs'
                >
                  Cancel
                </Button>
                <Button
                  size='sm'
                  onClick={handleTimingSave}
                  className='h-7 px-2 text-xs'
                  disabled={hasError}
                >
                  Save
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      )}
    </div>
  )
}
