'use client'

import React, { useState, useMemo, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import { ChevronDown, ChevronRight, Captions } from 'lucide-react'
import { CaptionEditor } from '../caption-editor'
import { useVideoStore } from '@/store/video-store'
import { Scene } from '@/types/video'

interface SceneCaptionEditorProps {
  onUpdateScene: (id: string, data: Partial<Scene>) => void
}

export function SceneCaptionEditor({ onUpdateScene }: SceneCaptionEditorProps) {
  const [isMainOpen, setIsMainOpen] = useState(false)
  const [openScenes, setOpenScenes] = useState<Set<string>>(new Set())
  const { scenes, project, updateProjectSpeechCaptions } = useVideoStore()

  // Helper function to check if a scene has captions
  const sceneHasCaptions = useCallback(
    (scene: Scene) => {
      // Check scene captions (text-to-video)
      if (scene.captions && scene.captions.length > 0) {
        return true
      }

      // Check speech captions (audio/podcast) - only for first scene typically
      if (
        project?.speech?.transcript?.captions &&
        project.speech.transcript.captions.length > 0
      ) {
        return true
      }

      return false
    },
    [project?.speech?.transcript?.captions]
  )

  // Get scenes with captions
  const scenesWithCaptions = useMemo(() => {
    return scenes.filter(sceneHasCaptions)
  }, [scenes, sceneHasCaptions])

  // Calculate total stats across all scenes
  const totalStats = useMemo(() => {
    let totalSegments = 0
    let totalWords = 0

    scenesWithCaptions.forEach(scene => {
      // Count scene captions
      if (scene.captions) {
        totalSegments += scene.captions.length
        totalWords += scene.captions.reduce(
          (sum, caption) => sum + (caption.words?.length || 0),
          0
        )
      }

      // Count speech captions (typically only for first scene)
      if (project?.speech?.transcript?.captions) {
        totalSegments += project.speech.transcript.captions.length
        totalWords += project.speech.transcript.captions.reduce(
          (sum, caption) => sum + (caption.wordBoundries?.length || 0),
          0
        )
      }
    })

    return { totalSegments, totalWords }
  }, [scenesWithCaptions, project?.speech?.transcript?.captions])

  // Toggle individual scene open/closed
  const toggleScene = (sceneId: string) => {
    const newOpenScenes = new Set(openScenes)
    if (newOpenScenes.has(sceneId)) {
      newOpenScenes.delete(sceneId)
    } else {
      newOpenScenes.add(sceneId)
    }
    setOpenScenes(newOpenScenes)
  }

  // If no scenes have captions, show appropriate message
  if (scenesWithCaptions.length === 0) {
    const isAudioProject =
      project?.method?.toLowerCase().includes('audio') ||
      project?.method?.toLowerCase().includes('podcast')

    return (
      <div className='rounded-lg border bg-card p-4'>
        <div className='flex items-center gap-2 text-sm text-muted-foreground'>
          <Captions className='w-4 h-4' />
          <span>
            {isAudioProject
              ? 'No captions available. Upload audio with transcription to create captions.'
              : 'No captions available. Generate voiceover for scenes to create captions.'}
          </span>
        </div>
      </div>
    )
  }

  return (
    <div className='rounded-lg border bg-card'>
      <Collapsible open={isMainOpen} onOpenChange={setIsMainOpen}>
        <CollapsibleTrigger asChild>
          <Button
            variant='ghost'
            className='w-full justify-between p-4 h-auto hover:bg-muted/50'
          >
            <div className='flex items-center gap-2'>
              {isMainOpen ? (
                <ChevronDown className='w-4 h-4' />
              ) : (
                <ChevronRight className='w-4 h-4' />
              )}
              <Captions className='w-4 h-4' />
              <span className='text-sm font-medium'>Caption Editor</span>
            </div>
            <div className='flex items-center gap-2'>
              <Badge variant='outline' className='text-xs'>
                {scenesWithCaptions.length} scenes
              </Badge>
              <Badge variant='outline' className='text-xs'>
                {totalStats.totalSegments} segments
              </Badge>
              <Badge variant='outline' className='text-xs'>
                {totalStats.totalWords} words
              </Badge>
            </div>
          </Button>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <div className='p-4 space-y-4'>
            {scenesWithCaptions.map(scene => {
              const sceneIndex = scenes.findIndex(s => s.id === scene.id)
              const isSceneOpen = openScenes.has(scene.id)

              // Calculate stats for this scene
              const sceneSegments = scene.captions?.length || 0
              const sceneWords =
                scene.captions?.reduce(
                  (sum, caption) => sum + (caption.words?.length || 0),
                  0
                ) || 0

              return (
                <div key={scene.id} className='border rounded-lg bg-background'>
                  <Collapsible
                    open={isSceneOpen}
                    onOpenChange={() => toggleScene(scene.id)}
                  >
                    <CollapsibleTrigger asChild>
                      <Button
                        variant='ghost'
                        className='w-full justify-between p-3 h-auto hover:bg-muted/30'
                      >
                        <div className='flex items-center gap-2'>
                          {isSceneOpen ? (
                            <ChevronDown className='w-3 h-3' />
                          ) : (
                            <ChevronRight className='w-3 h-3' />
                          )}
                          <span className='text-sm font-medium'>
                            Scene {sceneIndex + 1}
                          </span>
                        </div>
                        <div className='flex items-center gap-2'>
                          <Badge variant='secondary' className='text-xs'>
                            {sceneSegments} segments
                          </Badge>
                          <Badge variant='secondary' className='text-xs'>
                            {sceneWords} words
                          </Badge>
                        </div>
                      </Button>
                    </CollapsibleTrigger>

                    <CollapsibleContent>
                      <div className='px-3 pb-3'>
                        <CaptionEditor
                          scene={scene}
                          onUpdateScene={onUpdateScene}
                          onUpdateProjectSpeechCaptions={
                            updateProjectSpeechCaptions
                          }
                          isCollapsed={false}
                        />
                      </div>
                    </CollapsibleContent>
                  </Collapsible>
                </div>
              )
            })}
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  )
}
