import {
  pgTable,
  text,
  uuid,
  boolean,
  jsonb,
  timestamp,
  numeric,
  index,
  integer,
} from 'drizzle-orm/pg-core'

export const apiKeys = pgTable('api_keys', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  key: text('key').notNull().unique(),
  userId: text('user_id').notNull(),
  permissions: text('permissions').array().notNull().default([]),
  lastUsed: timestamp('last_used'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  isActive: boolean('is_active').default(true).notNull(),
})

export const projects = pgTable(
  'projects',
  {
    projectId: uuid('project_id').primaryKey().defaultRandom(),
    userId: text('user_id').notNull(),
    organizationId: text('organization_id'),
    projectName: text('project_name').notNull(),
    method: text('method').notNull(),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow(),
    coverColor: text('cover_color'),
    coverPic: text('cover_pic'),
    orientation: text('orientation').notNull(),
    duration: numeric('duration', { precision: 10, scale: 3 }),
    summary: text('summary'),
    voiceRegenerations: integer('voice_regenerations').notNull().default(0),
    speech: jsonb('speech').$type<{
      enabled: boolean
      src: string
      name: string
      volume: number
      transcript: {
        captions: Array<{
          start: number
          end: number
          sentence: string
          wordBoundries: Array<{ start: number; end: number; word: string }>
        }>
        status: 'QUEUED' | 'COMPLETED' | 'FAILED'
      }
    } | null>(),
    backgroundVideo: jsonb('background_video').$type<{
      src: string
      muted: boolean
    } | null>(),
    music: jsonb('music').$type<{
      enabled: boolean
      src: string
      volume: number
      duration: number
      name: string
    }>(),
    captionSettings: jsonb('caption_settings').$type<{
      enabled: boolean
      fontFamily: string
      fontSize: number
      fontWeight: string
      fontStyle: string
      textColor: string
      highlightColor: string
      backgroundColor: string
      backgroundOpacity: number
      textAlign: string
      textShadow: boolean
      borderRadius: number
      padding: number
      maxWidth: number
      animation: string
    }>(),
    scenes: jsonb('scenes').$type<Array<Record<string, unknown>>>(),
    eventId: text('event_id'),
    runId: text('run_id'),
    importedMedia: jsonb('imported_media').$type<string[]>(),
  },
  table => [
    index('idx_projects_user_id').on(table.userId),
    index('idx_projects_organization_id').on(table.organizationId),
  ]
)

export const mediaAssets = pgTable(
  'media_assets',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    userId: text('user_id').notNull(),
    fileName: text('file_name').notNull(),
    originalName: text('original_name').notNull(),
    mimeType: text('mime_type').notNull(),
    fileSize: integer('file_size').notNull(), // in bytes

    // URLs
    originalUrl: text('original_url').notNull(),
    thumbnailUrl: text('thumbnail_url'),
    lowResUrl: text('low_res_url'),

    // Dimensions
    width: integer('width'),
    height: integer('height'),

    // Video specific
    duration: numeric('duration', { precision: 10, scale: 3 }), // in seconds
    quality: text('quality'), // 'hd', 'sd', 'uhd', etc.
    fps: integer('fps'),

    // Metadata
    metadata: jsonb('metadata').$type<{
      aspectRatio?: number
      orientation?: 'landscape' | 'portrait' | 'square'
      dominantColors?: string[]
      tags?: string[]
      description?: string
      alt?: string
    }>(),

    // Timestamps
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow(),
  },
  table => [
    index('idx_media_assets_user_id').on(table.userId),
    index('idx_media_assets_mime_type').on(table.mimeType),
    index('idx_media_assets_created_at').on(table.createdAt),
  ]
)

export const stockMusic = pgTable('stock_music', {
  id: uuid('id').primaryKey().defaultRandom(),
  title: text('title').notNull(),
  genre: text('genre').notNull(),
  mood: text('mood').notNull(),
  artistName: text('artist_name').notNull(),
  artistUrl: text('artist_url'),
  provider: text('provider').notNull(),
  licenseId: text('license_id').notNull(),
  sourceUrl: text('source_url'),
  previewUrl: text('preview_url').notNull(),
  durationMillis: integer('duration_millis').notNull(),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow(),
})

export const renderJobs = pgTable(
  'render_jobs',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    projectId: text('project_id').notNull(),
    userId: text('user_id').notNull(),
    organizationId: text('organization_id'),
    status: text('status').notNull(), // 'initializing', 'rendering', 'completed', 'failed'
    progress: integer('progress').default(0), // 0-100
    publicUrl: text('public_url'),
    thumbnailUrl: text('thumbnail_url'),
    errorMessage: text('error_message'),
    renderMethod: text('render_method'), // 'lambda' or 'cloudrun'
    exportName: text('export_name'), // now nullable
    exportResolution: text('export_resolution'), // now nullable
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow(),
    youtubeId: text('youtube_id'),
  },
  table => [
    index('idx_render_jobs_user_project').on(table.userId, table.projectId),
    index('idx_render_jobs_organization_id').on(table.organizationId),
    index('idx_render_jobs_status').on(table.status),
    index('idx_render_jobs_created_at').on(table.createdAt),
  ]
)

export const youtubeConnections = pgTable(
  'youtube_connections',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    userId: text('user_id').notNull(), // Multiple connections per user allowed
    organizationId: text('organization_id'),

    // Encrypted OAuth tokens
    accessToken: text('access_token').notNull(),
    refreshToken: text('refresh_token').notNull(),
    expiresAt: timestamp('expires_at', { withTimezone: true }).notNull(),

    // YouTube channel information
    channelId: text('channel_id').notNull(),
    channelTitle: text('channel_title').notNull(),
    channelThumbnailUrl: text('channel_thumbnail_url'),
    channelDescription: text('channel_description'),

    // Connection metadata
    scopes: text('scopes').array().notNull(), // Granted OAuth scopes
    connectedAt: timestamp('connected_at', { withTimezone: true }).defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow(),
    isActive: boolean('is_active').default(true).notNull(),
  },
  table => [
    index('idx_youtube_connections_user_id').on(table.userId),
    index('idx_youtube_connections_channel_id').on(table.channelId),
    index('idx_youtube_connections_expires_at').on(table.expiresAt),
    // Compound index for the most common query pattern (user_id + is_active + expires_at)
    index('idx_youtube_connections_user_active_expires').on(
      table.userId,
      table.isActive,
      table.expiresAt
    ),
  ]
)

export const user = pgTable(
  'user',
  {
    id: text('id').primaryKey(),
    name: text('name').notNull(),
    email: text('email').notNull().unique(),
    emailVerified: boolean('email_verified')
      .$defaultFn(() => false)
      .notNull(),
    image: text('image'),
    createdAt: timestamp('created_at')
      .$defaultFn(() => /* @__PURE__ */ new Date())
      .notNull(),
    updatedAt: timestamp('updated_at')
      .$defaultFn(() => /* @__PURE__ */ new Date())
      .notNull(),
    stripeCustomerId: text('stripe_customer_id'),
    normalizedEmail: text('normalized_email').unique(),
    // Admin plugin fields
    role: text('role').default('user'),
    banned: boolean('banned').default(false),
    banReason: text('ban_reason'),
    banExpires: timestamp('ban_expires'),
  },
  table => [index('idx_user_email').on(table.email)]
)

export const session = pgTable(
  'session',
  {
    id: text('id').primaryKey(),
    expiresAt: timestamp('expires_at').notNull(),
    token: text('token').notNull().unique(),
    createdAt: timestamp('created_at').notNull(),
    updatedAt: timestamp('updated_at').notNull(),
    ipAddress: text('ip_address'),
    userAgent: text('user_agent'),
    userId: text('user_id')
      .notNull()
      .references(() => user.id, { onDelete: 'cascade' }),
    activeOrganizationId: text('active_organization_id'),
    // Admin plugin field
    impersonatedBy: text('impersonated_by'),
  },
  table => [
    index('idx_session_user_id').on(table.userId),
    index('idx_session_token').on(table.token),
  ]
)

export const account = pgTable(
  'account',
  {
    id: text('id').primaryKey(),
    accountId: text('account_id').notNull(),
    providerId: text('provider_id').notNull(),
    userId: text('user_id')
      .notNull()
      .references(() => user.id, { onDelete: 'cascade' }),
    accessToken: text('access_token'),
    refreshToken: text('refresh_token'),
    idToken: text('id_token'),
    accessTokenExpiresAt: timestamp('access_token_expires_at'),
    refreshTokenExpiresAt: timestamp('refresh_token_expires_at'),
    scope: text('scope'),
    password: text('password'),
    createdAt: timestamp('created_at').notNull(),
    updatedAt: timestamp('updated_at').notNull(),
  },
  table => [index('idx_account_user_id').on(table.userId)]
)

export const verification = pgTable(
  'verification',
  {
    id: text('id').primaryKey(),
    identifier: text('identifier').notNull(),
    value: text('value').notNull(),
    expiresAt: timestamp('expires_at').notNull(),
    createdAt: timestamp('created_at').$defaultFn(
      () => /* @__PURE__ */ new Date()
    ),
    updatedAt: timestamp('updated_at').$defaultFn(
      () => /* @__PURE__ */ new Date()
    ),
  },
  table => [index('idx_verification_identifier').on(table.identifier)]
)

export const organization = pgTable(
  'organization',
  {
    id: text('id').primaryKey(),
    name: text('name').notNull(),
    slug: text('slug').unique(),
    logo: text('logo'),
    createdAt: timestamp('created_at').notNull(),
    metadata: text('metadata'),
  },
  table => [index('idx_organization_slug').on(table.slug)]
)

export const member = pgTable(
  'member',
  {
    id: text('id').primaryKey(),
    organizationId: text('organization_id')
      .notNull()
      .references(() => organization.id, { onDelete: 'cascade' }),
    userId: text('user_id')
      .notNull()
      .references(() => user.id, { onDelete: 'cascade' }),
    role: text('role').default('member').notNull(),
    createdAt: timestamp('created_at').notNull(),
  },
  table => [
    index('idx_member_user_id').on(table.userId),
    index('idx_member_organization_id').on(table.organizationId),
  ]
)

export const invitation = pgTable(
  'invitation',
  {
    id: text('id').primaryKey(),
    organizationId: text('organization_id')
      .notNull()
      .references(() => organization.id, { onDelete: 'cascade' }),
    email: text('email').notNull(),
    role: text('role'),
    status: text('status').default('pending').notNull(),
    expiresAt: timestamp('expires_at').notNull(),
    inviterId: text('inviter_id')
      .notNull()
      .references(() => user.id, { onDelete: 'cascade' }),
  },
  table => [
    index('idx_invitation_email').on(table.email),
    index('idx_invitation_organization_id').on(table.organizationId),
  ]
)

export const usage = pgTable(
  'usage',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    organizationId: text('organization_id').notNull(),
    members: text('members').array().notNull(), // Array of user IDs

    // Usage tracking
    projectsUsed: integer('projects_used').default(0),
    videoExportsUsed: integer('video_exports_used').notNull().default(0),
    aiImagesUsed: integer('ai_images_used').notNull().default(0),
    teamMembersUsed: integer('team_members_used').notNull().default(1),
    storageUsed: integer('storage_used').notNull().default(0),

    // Plan & Period management
    planType: text('plan_type').notNull(), // 'free', 'pro', 'enterprise'
    planStatus: text('plan_status').notNull().default('active'), // active, canceled, past_due
    currentPeriodStart: timestamp('current_period_start', {
      withTimezone: true,
    }).defaultNow(),
    currentPeriodEnd: timestamp('current_period_end', { withTimezone: true }),

    // Timestamps
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow(),
  },
  table => [
    index('idx_usage_organization_id').on(table.organizationId),
    index('idx_usage_members').on(table.members),
  ]
)

export const subscription = pgTable(
  'subscription',
  {
    id: text('id').primaryKey(),
    plan: text('plan').notNull(),
    referenceId: text('reference_id').notNull(),
    stripeCustomerId: text('stripe_customer_id'),
    stripeSubscriptionId: text('stripe_subscription_id'),
    status: text('status').default('incomplete'),
    periodStart: timestamp('period_start'),
    periodEnd: timestamp('period_end'),
    cancelAtPeriodEnd: boolean('cancel_at_period_end'),
    seats: integer('seats'),
    trialStart: timestamp('trial_start'),
    trialEnd: timestamp('trial_end'),
  },
  table => [
    index('idx_subscription_stripe_customer_id').on(table.stripeCustomerId),
  ]
)

export const schema = {
  user,
  session,
  account,
  verification,
  organization,
  member,
  invitation,
  subscription,
}
