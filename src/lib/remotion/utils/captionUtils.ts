/**
 * Caption generation utilities for Remotion components
 *
 * These utilities handle caption generation from various sources including
 * ElevenLabs alignment data and basic text processing.
 */

// Note: Using 'any' type for alignment to handle both old and new ElevenLabs formats
// Old format: { characters: string[], character_start_times_seconds: number[], character_end_times_seconds: number[] }
// New format: { characters: Array<{ text: string, start: number, end: number }> }
// Type representing both old and new ElevenLabs alignment formats
export type ElevenLabsAlignment =
  | {
      characters: Array<{ text: string; start: number; end: number }>
    }
  | {
      characters: string[]
      character_start_times_seconds: number[]
      character_end_times_seconds: number[]
    }

// Utility: detect pause-tag related tokens produced by ElevenLabs
const isPauseToken = (word: string): boolean => {
  const w = (word || '').trim().toLowerCase()
  if (!w) return false
  // Match typical SSML break fragments seen in alignment words
  if (w.startsWith('<break')) return true
  if (w === '/>' || w === '>' || w === '<' || w === '<br>' || w === '<br/>')
    return true
  if (/^time\s*=\s*['"][0-9]+(\.[0-9]+)?s['"]$/.test(w)) return true
  // Whole-tag forms like <break time="1.0s"/>
  if (/<\s*break\b[^>]*\/?\s*>/.test(w)) return true
  return false
}

// Utility: strip full pause tags from a plain text string (for basic caption fallbacks)
const stripPauseTagsFromText = (input: string): string => {
  if (!input) return input
  // Replace SSML break tags with a single space to preserve spacing
  const cleaned = input.replace(/<\s*break\b[^>]*\/?\s*>/gi, ' ')
  return cleaned.replace(/\s+/g, ' ').trim()
}

/**
 * Generate legacy captions from ElevenLabs alignment data
 * This function is used by Inngest functions for backward compatibility
 * Returns the old format with 'sentence' and 'wordBoundries'
 *
 * Supports both old and new ElevenLabs alignment formats
 */
export function generateLegacyCaptionsFromElevenLabs(
  text: string,
  alignment: ElevenLabsAlignment | null | undefined,
  duration: number
) {
  console.log(
    '🔍 generateLegacyCaptionsFromElevenLabs called with alignment:',
    alignment
  )

  if (!alignment || !alignment.characters) {
    console.log('⚠️ No alignment data, using basic legacy captions')
    return generateBasicLegacyCaptions(text, duration)
  }

  // Check if this is the new format (array of objects with text, start, end)
  const isNewFormat =
    Array.isArray(alignment.characters) &&
    alignment.characters.length > 0 &&
    typeof alignment.characters[0] === 'object' &&
    'text' in alignment.characters[0] &&
    'start' in alignment.characters[0] &&
    'end' in alignment.characters[0]

  console.log(
    `📊 Legacy function detected alignment format: ${isNewFormat ? 'NEW' : 'OLD'}`
  )

  const words: Array<{ word: string; start: number; end: number }> = []

  if (isNewFormat) {
    // Handle new format: characters is array of {text, start, end} objects
    const newAlignment = alignment as {
      characters: Array<{ text: string; start: number; end: number }>
    }
    let currentWord = ''
    let wordStart = 0
    let wordEnd = 0

    for (let i = 0; i < newAlignment.characters.length; i++) {
      const charData = newAlignment.characters[i]
      const char = charData.text
      const start = charData.start
      const end = charData.end

      // Always add non-space characters to the current word first
      if (char !== ' ') {
        if (currentWord === '') {
          wordStart = start
        }
        currentWord += char
        wordEnd = end
      }

      // Then check if we should finish the current word (space or last character)
      if (char === ' ' || i === newAlignment.characters.length - 1) {
        const trimmed = currentWord.trim()
        if (trimmed && !isPauseToken(trimmed)) {
          words.push({
            word: trimmed,
            start: wordStart,
            end: wordEnd,
          })
        }
        currentWord = ''
        wordStart =
          i < newAlignment.characters.length - 1
            ? newAlignment.characters[i + 1].start
            : end
      }
    }
  } else {
    // Handle old format: separate arrays for characters, starts, ends
    const oldAlignment = alignment as {
      characters: string[]
      character_start_times_seconds: number[]
      character_end_times_seconds: number[]
    }
    if (
      !Array.isArray(oldAlignment.characters) ||
      !Array.isArray(oldAlignment.character_start_times_seconds) ||
      !Array.isArray(oldAlignment.character_end_times_seconds)
    ) {
      console.error(
        '❌ Invalid old format alignment data structure in legacy function'
      )
      return generateBasicLegacyCaptions(text, duration)
    }

    const chars = oldAlignment.characters
    const starts = oldAlignment.character_start_times_seconds
    const ends = oldAlignment.character_end_times_seconds

    let currentWord = ''
    let wordStart = 0

    for (let i = 0; i < chars.length; i++) {
      const char = chars[i]
      const start = starts[i]
      const end = ends[i]

      if (start === undefined || end === undefined) {
        console.error(`❌ Undefined timing at index ${i} in legacy function:`, {
          char,
          start,
          end,
        })
        continue
      }

      // Always add non-space characters to the current word first
      if (char !== ' ') {
        if (currentWord === '') {
          wordStart = start
        }
        currentWord += char
      }

      // Then check if we should finish the current word (space or last character)
      if (char === ' ' || i === chars.length - 1) {
        const trimmed = currentWord.trim()
        if (trimmed && !isPauseToken(trimmed)) {
          words.push({
            word: trimmed,
            start: wordStart,
            end: end,
          })
        }
        currentWord = ''
        wordStart = i < chars.length - 1 ? starts[i + 1] : end
      }
    }
  }

  // Group words into sentences (4 words per sentence)
  const captions = []
  const wordsPerSentence = 4

  for (let i = 0; i < words.length; i += wordsPerSentence) {
    const sentenceWords = words.slice(i, i + wordsPerSentence)
    if (sentenceWords.length > 0) {
      captions.push({
        start: sentenceWords[0].start,
        end: sentenceWords[sentenceWords.length - 1].end,
        sentence: sentenceWords.map(w => w.word).join(' '), // Legacy format for Inngest
        wordBoundries: sentenceWords.map(w => ({
          start: w.start,
          end: w.end,
          word: w.word,
        })), // Legacy format for Inngest
      })
    }
  }

  console.log(
    `✅ Legacy function generated ${captions.length} caption segments`
  )

  // Debug: Log the last few words to verify final characters are preserved
  if (words.length > 0) {
    const lastWord = words[words.length - 1]
    const reconstructedText = words.map(w => w.word).join(' ')
    console.log(
      `🔍 Legacy last word extracted: "${lastWord.word}" (should include final punctuation)`
    )
    console.log(
      `📝 Legacy Original text length: ${text.length}, Reconstructed length: ${reconstructedText.length}`
    )
    console.log(`📝 Legacy Original: "${text}"`)
    console.log(`📝 Legacy Reconstructed: "${reconstructedText}"`)
  }
  return captions
}

/**
 * Generate captions from ElevenLabs alignment data (modern version)
 * This is the updated version used by the scene editor components
 * Returns the new format with 'text' and 'words'
 *
 * Supports both old and new ElevenLabs alignment formats:
 * - Old format: { characters: string[], character_start_times_seconds: number[], character_end_times_seconds: number[] }
 * - New format: { characters: Array<{ text: string, start: number, end: number }> }
 */
export function generateCaptionsFromElevenLabs(
  text: string,
  alignment: ElevenLabsAlignment | null | undefined,
  duration: number
) {
  console.log('🔍 generateCaptionsFromElevenLabs called with:', {
    textLength: text.length,
    alignmentType: typeof alignment,
    alignmentKeys: alignment ? Object.keys(alignment) : null,
    charactersType: alignment?.characters ? typeof alignment.characters : null,
    charactersLength: alignment?.characters
      ? alignment.characters.length
      : null,
    firstCharacter: alignment?.characters?.[0],
    duration,
  })

  if (!alignment || !alignment.characters) {
    console.log('⚠️ No alignment data, using basic captions')
    return generateBasicCaptions(text, duration)
  }

  // Check if this is the new format (array of objects with text, start, end)
  const isNewFormat =
    Array.isArray(alignment.characters) &&
    alignment.characters.length > 0 &&
    typeof alignment.characters[0] === 'object' &&
    'text' in alignment.characters[0] &&
    'start' in alignment.characters[0] &&
    'end' in alignment.characters[0]

  console.log(`📊 Detected alignment format: ${isNewFormat ? 'NEW' : 'OLD'}`)

  const words: Array<{ word: string; start: number; end: number }> = []

  if (isNewFormat) {
    // Handle new format: characters is array of {text, start, end} objects
    console.log('🆕 Processing new alignment format')
    const newAlignment = alignment as {
      characters: Array<{ text: string; start: number; end: number }>
    }
    let currentWord = ''
    let wordStart = 0
    let wordEnd = 0

    for (let i = 0; i < newAlignment.characters.length; i++) {
      const charData = newAlignment.characters[i]
      const char = charData.text
      const start = charData.start
      const end = charData.end

      // Always add non-space characters to the current word first
      if (char !== ' ') {
        if (currentWord === '') {
          wordStart = start
        }
        currentWord += char
        wordEnd = end
      }

      // Then check if we should finish the current word (space or last character)
      if (char === ' ' || i === newAlignment.characters.length - 1) {
        const trimmed = currentWord.trim()
        if (trimmed && !isPauseToken(trimmed)) {
          words.push({
            word: trimmed,
            start: wordStart,
            end: wordEnd,
          })
        }
        currentWord = ''
        wordStart =
          i < newAlignment.characters.length - 1
            ? newAlignment.characters[i + 1].start
            : end
      }
    }
  } else {
    // Handle old format: separate arrays for characters, starts, ends
    console.log('🔄 Processing old alignment format')

    const oldAlignment = alignment as {
      characters: string[]
      character_start_times_seconds: number[]
      character_end_times_seconds: number[]
    }
    if (
      !Array.isArray(oldAlignment.characters) ||
      !Array.isArray(oldAlignment.character_start_times_seconds) ||
      !Array.isArray(oldAlignment.character_end_times_seconds)
    ) {
      console.error('❌ Invalid old format alignment data structure')
      return generateBasicCaptions(text, duration)
    }

    const chars = oldAlignment.characters
    const starts = oldAlignment.character_start_times_seconds
    const ends = oldAlignment.character_end_times_seconds

    if (chars.length !== starts.length || chars.length !== ends.length) {
      console.error('❌ Mismatched array lengths in alignment data:', {
        chars: chars.length,
        starts: starts.length,
        ends: ends.length,
      })
      return generateBasicCaptions(text, duration)
    }

    let currentWord = ''
    let wordStart = 0

    for (let i = 0; i < chars.length; i++) {
      const char = chars[i]
      const start = starts[i]
      const end = ends[i]

      if (start === undefined || end === undefined) {
        console.error(`❌ Undefined timing at index ${i}:`, {
          char,
          start,
          end,
        })
        continue
      }

      // Always add non-space characters to the current word first
      if (char !== ' ') {
        if (currentWord === '') {
          wordStart = start
        }
        currentWord += char
      }

      // Then check if we should finish the current word (space or last character)
      if (char === ' ' || i === chars.length - 1) {
        const trimmed = currentWord.trim()
        if (trimmed && !isPauseToken(trimmed)) {
          words.push({
            word: trimmed,
            start: wordStart,
            end: end,
          })
        }
        currentWord = ''
        wordStart = i < chars.length - 1 ? starts[i + 1] : end
      }
    }
  }

  console.log(`✅ Extracted ${words.length} words from alignment data`)

  // Debug: Log the last few words to verify final characters are preserved
  if (words.length > 0) {
    const lastWord = words[words.length - 1]
    const reconstructedText = words.map(w => w.word).join(' ')
    console.log(
      `🔍 Last word extracted: "${lastWord.word}" (should include final punctuation)`
    )
    console.log(
      `📝 Original text length: ${text.length}, Reconstructed length: ${reconstructedText.length}`
    )
    console.log(`📝 Original: "${text}"`)
    console.log(`📝 Reconstructed: "${reconstructedText}"`)
  }

  // Group words into sentences (4 words per sentence)
  const captions = []
  const wordsPerSentence = 4

  for (let i = 0; i < words.length; i += wordsPerSentence) {
    const sentenceWords = words.slice(i, i + wordsPerSentence)
    if (sentenceWords.length > 0) {
      captions.push({
        start: sentenceWords[0].start,
        end: sentenceWords[sentenceWords.length - 1].end,
        text: sentenceWords.map(w => w.word).join(' '), // Modern format
        words: sentenceWords.map(w => ({
          start: w.start,
          end: w.end,
          word: w.word,
        })), // Modern format
      })
    }
  }

  console.log(`✅ Generated ${captions.length} caption segments`)
  return captions
}

/**
 * Generate basic legacy captions when no alignment data is available
 * Returns the old format with 'sentence' and 'wordBoundries'
 */
export function generateBasicLegacyCaptions(text: string, duration: number) {
  const cleanedText = stripPauseTagsFromText(text)
  const rawSentences = cleanedText
    .split(/[.!?]+/)
    .map(s => s.trim())
    .filter(Boolean)

  // Build processed sentences with pause tokens removed from words
  const processed = rawSentences
    .map(s => {
      const words = s
        .split(/\s+/)
        .filter(Boolean)
        .filter(w => !isPauseToken(w))
      return { text: words.join(' '), words }
    })
    .filter(item => item.words.length > 0)

  if (processed.length === 0) return []

  const timePerSentence = duration / processed.length

  return processed.map((item, index) => {
    const words = item.words
    const sentence = item.text
    const wordDuration = timePerSentence / Math.max(1, words.length)

    return {
      start: index * timePerSentence,
      end: (index + 1) * timePerSentence,
      sentence,
      wordBoundries: words.map((word, wordIndex) => ({
        start: index * timePerSentence + wordIndex * wordDuration,
        end: index * timePerSentence + (wordIndex + 1) * wordDuration,
        word,
      })),
    }
  })
}

/**
 * Generate basic captions when no alignment data is available
 * Splits text into sentences and estimates timing
 * Returns the new format with 'text' and 'words'
 */
export function generateBasicCaptions(text: string, duration: number) {
  const cleanedText = stripPauseTagsFromText(text)
  const rawSentences = cleanedText
    .split(/[.!?]+/)
    .map(s => s.trim())
    .filter(Boolean)

  const processed = rawSentences
    .map(s => {
      const words = s
        .split(/\s+/)
        .filter(Boolean)
        .filter(w => !isPauseToken(w))
      return { text: words.join(' '), words }
    })
    .filter(item => item.words.length > 0)

  if (processed.length === 0) return []

  const timePerSentence = duration / processed.length

  return processed.map((item, index) => {
    const words = item.words
    const sentenceText = item.text
    const wordDuration = timePerSentence / Math.max(1, words.length)

    return {
      start: index * timePerSentence,
      end: (index + 1) * timePerSentence,
      text: sentenceText,
      words: words.map((word, wordIndex) => ({
        start: index * timePerSentence + wordIndex * wordDuration,
        end: index * timePerSentence + (wordIndex + 1) * wordDuration,
        word,
      })),
    }
  })
}

/**
 * Estimate duration from text length
 * Used as fallback when audio duration is not available
 */
export function estimateDurationFromText(text: string): number {
  // Rough estimate: ~150 words per minute, ~5 characters per word
  const charactersPerSecond = (150 * 5) / 60
  return Math.max(1, text.length / charactersPerSecond)
}
