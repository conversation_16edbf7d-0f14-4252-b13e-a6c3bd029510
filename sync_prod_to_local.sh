set -e

# Variables
DB_URL="postgres://postgres.fqoolhppmbqqzxqqtjbn:<EMAIL>:5432/postgres?sslmode=require"
SCHEMA_FILE="supabase/_dumps/prod-schema.sql"
DATA_FILE="supabase/_dumps/prod-data.sql"
CONTAINER="supabase_db_adori-ai-v2"
TABLES="account, api_keys, invitation, media_assets, member, organization, projects, render_jobs, session, stock_music, subscription, usage, \"user\", verification, youtube_connections"

# Dump schema
echo "Dumping schema from production..."
npx supabase db dump --db-url "$DB_URL" --file "$SCHEMA_FILE"

# Dump data
echo "Dumping data from production..."
npx supabase db dump --db-url "$DB_URL" --data-only --file "$DATA_FILE"

# Truncate local tables
echo "Truncating local tables..."
docker exec -i $CONTAINER psql -U postgres -d postgres -c "TRUNCATE TABLE $TABLES RESTART IDENTITY CASCADE;"

# Import schema
echo "Importing schema to local database..."
docker exec -i $CONTAINER psql -U postgres -d postgres -f - < $SCHEMA_FILE

# Import data
echo "Importing data to local database..."
docker exec -i $CONTAINER psql -U postgres -d postgres -f - < $DATA_FILE

# List tables for verification
echo "Listing tables in local database..."
docker exec -i $CONTAINER psql -U postgres -d postgres -c "\dt public.*"

echo "Database sync complete."
